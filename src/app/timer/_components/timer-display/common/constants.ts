import { TimerColorPreset } from '@/lib/pomodoro-store';
import { usePomodoroStore } from '@/lib/pomodoro-store';

// Position classes for the timer
export const positionClasses = {
  'top-left': 'top-4 left-4',
  'top-right': 'top-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'center': 'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
  'top-center': 'top-4 left-1/2 -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
  'middle-left': 'top-1/2 left-4 -translate-y-1/2',
  'middle-right': 'top-1/2 right-4 -translate-y-1/2',
  
  // Simplified bottom positions (5 positions)
  'bottom-left-center': 'bottom-4 left-[25%]',
  'bottom-right-center': 'bottom-4 left-[75%]',
};

// Bottom edge padding constant
export const BOTTOM_EDGE_PADDING = 16; // 16px padding from bottom edge

// Color utility functions
export const getButtonTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'text-white';
    case 'blue': return 'text-blue-400';
    case 'green': return 'text-green-400';
    case 'yellow': return 'text-amber-400';
    case 'red': return 'text-rose-400';
    case 'purple': return 'text-purple-400';
    case 'indigo': return 'text-indigo-400';
    case 'orange': return 'text-orange-400';
    case 'pink': return 'text-pink-400';
    case 'default':
    default: return 'text-white';
  }
};

export const getButtonHoverEffect = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'hover:bg-white/15';
    case 'blue': return 'hover:bg-blue-500/15';
    case 'green': return 'hover:bg-green-500/15';
    case 'yellow': return 'hover:bg-amber-400/15';
    case 'red': return 'hover:bg-rose-500/15';
    case 'purple': return 'hover:bg-purple-500/15';
    case 'indigo': return 'hover:bg-indigo-500/15';
    case 'orange': return 'hover:bg-orange-500/15';
    case 'pink': return 'hover:bg-pink-500/15';
    default: return 'hover:bg-white/15';
  }
};

export const getButtonActiveColor = (timerColor: TimerColorPreset, isRunning: boolean) => {
  switch (timerColor) {
    case 'white': return isRunning ? 'bg-white/10' : '';
    case 'blue': return isRunning ? 'bg-blue-500/10' : '';
    case 'green': return isRunning ? 'bg-green-500/10' : '';
    case 'yellow': return isRunning ? 'bg-amber-400/10' : '';
    case 'red': return isRunning ? 'bg-rose-500/10' : '';
    case 'purple': return isRunning ? 'bg-purple-500/10' : '';
    case 'indigo': return isRunning ? 'bg-indigo-500/10' : '';
    case 'orange': return isRunning ? 'bg-orange-500/10' : '';
    case 'pink': return isRunning ? 'bg-pink-500/10' : '';
    case 'default':
    default: return isRunning ? 'bg-white/10' : '';
  }
};

export const getProgressBarColor = (timerColor: TimerColorPreset) => {
  const currentPhase = usePomodoroStore.getState().currentPhase;

  // Base on timer color setting first
  switch (timerColor) {
    case 'blue':
      return 'bg-gradient-to-r from-blue-500 to-blue-400';
    case 'green':
      return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
    case 'yellow':
      return 'bg-gradient-to-r from-amber-400 to-amber-300';
    case 'red':
      return 'bg-gradient-to-r from-rose-500 to-rose-400';
    case 'purple':
      return 'bg-gradient-to-r from-purple-500 to-purple-400';
    case 'indigo':
      return 'bg-gradient-to-r from-indigo-500 to-indigo-400';
    case 'orange':
      return 'bg-gradient-to-r from-orange-500 to-orange-400';
    case 'pink':
      return 'bg-gradient-to-r from-pink-500 to-pink-400';
    case 'white':
      // If white color is selected, use white color for progress bar
      return 'bg-gradient-to-r from-white to-gray-100';
    case 'default':
    default:
      // Default uses phase-specific colors for better visual feedback
      switch (currentPhase) {
        case 'pomodoro':
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
        case 'shortBreak':
          return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
        case 'longBreak':
          return 'bg-gradient-to-r from-purple-500 to-purple-400';
        default:
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
      }
  }
};

export const getTimerTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-400';
    case 'green':
      return 'text-emerald-400';
    case 'yellow':
      return 'text-amber-400';
    case 'red':
      return 'text-rose-400';
    case 'purple':
      return 'text-purple-400';
    case 'indigo':
      return 'text-indigo-400';
    case 'orange':
      return 'text-orange-400';
    case 'pink':
      return 'text-pink-400';
    case 'white':
      return 'text-white';
    case 'default':
    default:
      // For default mode, use white for focus and phase-specific colors for breaks
      const currentPhase = usePomodoroStore.getState().currentPhase;
      switch (currentPhase) {
        case 'pomodoro':
          return 'text-white';
        case 'shortBreak':
          return 'text-emerald-400';
        case 'longBreak':
          return 'text-purple-400';
        default:
          return 'text-white';
      }
  }
};

export const getTimerTextColorValue = (timerColor: TimerColorPreset) => {
  // Higher opacity (0.95) for better contrast against backgrounds
  switch (timerColor) {
    case 'white': return 'rgba(255, 255, 255, 0.95)';
    case 'blue': return 'rgba(59, 130, 246, 0.95)'; // blue-400
    case 'green': return 'rgba(16, 185, 129, 0.95)'; // emerald-500
    case 'yellow': return 'rgba(251, 191, 36, 0.95)'; // amber-400
    case 'red': return 'rgba(239, 68, 68, 0.95)'; // rose-500
    case 'purple': return 'rgba(168, 85, 247, 0.95)'; // purple-500
    case 'indigo': return 'rgba(99, 102, 241, 0.95)'; // indigo-500
    case 'orange': return 'rgba(251, 146, 60, 0.95)'; // orange-400
    case 'pink': return 'rgba(244, 114, 182, 0.95)'; // pink-400
    case 'default':
    default:
      // For default mode, use white for focus and phase-specific colors for breaks
      const currentPhase = usePomodoroStore.getState().currentPhase;
      switch (currentPhase) {
        case 'pomodoro':
          return 'rgba(255, 255, 255, 0.95)'; // white
        case 'shortBreak':
          return 'rgba(16, 185, 129, 0.95)'; // emerald-500
        case 'longBreak':
          return 'rgba(168, 85, 247, 0.95)'; // purple-500
        default:
          return 'rgba(255, 255, 255, 0.95)'; // white
      }
  }
};

export const getTimerTextShadow = (timerColor: TimerColorPreset) => {
  const currentPhase = usePomodoroStore.getState().currentPhase;
  const isRunning = usePomodoroStore.getState().isRunning;
  
  // Base shadow for readability
  const baseShadow = '0 1px 3px rgba(0, 0, 0, 0.2)';
  
  // Enhanced shadow based on state and color
  if (isRunning) {
    switch (timerColor) {
      case 'blue':
        return `${baseShadow}, 0 0 8px rgba(59, 130, 246, 0.3)`;
      case 'green':
        return `${baseShadow}, 0 0 8px rgba(16, 185, 129, 0.3)`;
      case 'yellow':
        return `${baseShadow}, 0 0 8px rgba(251, 191, 36, 0.3)`;
      case 'red':
        return `${baseShadow}, 0 0 8px rgba(239, 68, 68, 0.3)`;
      case 'purple':
        return `${baseShadow}, 0 0 8px rgba(139, 92, 246, 0.3)`;
      case 'indigo':
        return `${baseShadow}, 0 0 8px rgba(99, 102, 241, 0.3)`;
      case 'orange':
        return `${baseShadow}, 0 0 8px rgba(249, 115, 22, 0.3)`;
      case 'pink':
        return `${baseShadow}, 0 0 8px rgba(236, 72, 153, 0.3)`;
      case 'white':
        return `${baseShadow}, 0 0 8px rgba(255, 255, 255, 0.3)`;
      case 'default':
      default:
        // Phase-specific glow when running with default color
        switch (currentPhase) {
          case 'pomodoro':
            return `${baseShadow}, 0 0 8px rgba(255, 255, 255, 0.2)`;
          case 'shortBreak':
            return `${baseShadow}, 0 0 8px rgba(16, 185, 129, 0.2)`;
          case 'longBreak':
            return `${baseShadow}, 0 0 8px rgba(139, 92, 246, 0.2)`;
          default:
            return baseShadow;
        }
    }
  }
  
  // Default shadow
  return baseShadow;
};

export const getBackgroundOpacity = (timerOpacity: number) => {
  // Return the exact opacity value set by user
  // No minimum threshold - allow full transparency if user wants it
  return timerOpacity;
};

export function getPhaseGradient() {
  const currentPhase = usePomodoroStore.getState().currentPhase;
  
  switch (currentPhase) {
    case 'pomodoro':
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
    case 'shortBreak':
      return 'linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.06))';
    case 'longBreak':
      return 'linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.06))';
    default:
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
  }
} 